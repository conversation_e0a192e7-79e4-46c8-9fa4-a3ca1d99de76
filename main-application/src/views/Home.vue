<template>
  <div class="home">
    <h2>首页卡片展示</h2>
    <div class="card-container">
      <div
        v-for="(card, index) in cards"
        :key="index"
        class="card"
      >
        <div class="card-header">
          <h3>{{ card.title }}</h3>
        </div>
        <div class="card-body">
          <p>{{ card.description }}</p>
          <div class="card-meta">
            <span class="tag">{{ card.category }}</span>
            <span class="date">{{ card.date }}</span>
          </div>
        </div>
        <div class="card-footer">
          <button class="btn-primary">查看详情</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Home',
  data() {
    return {
      cards: [
        {
          title: '卡片标题 1',
          description: '这是第一个卡片的描述内容，展示了卡片的基本信息和功能介绍。',
          category: '分类A',
          date: '2024-01-15'
        },
        {
          title: '卡片标题 2',
          description: '这是第二个卡片的描述内容，包含了更多详细的信息和特性说明。',
          category: '分类B',
          date: '2024-01-16'
        },
        {
          title: '卡片标题 3',
          description: '这是第三个卡片的描述内容，提供了丰富的功能和用户体验。',
          category: '分类C',
          date: '2024-01-17'
        },
        {
          title: '卡片标题 4',
          description: '这是第四个卡片的描述内容，展示了系统的强大功能和灵活性。',
          category: '分类A',
          date: '2024-01-18'
        },
        {
          title: '卡片标题 5',
          description: '这是第五个卡片的描述内容，演示了换行效果和响应式布局。',
          category: '分类B',
          date: '2024-01-19'
        },
        {
          title: '卡片标题 6',
          description: '这是第六个卡片的描述内容，继续展示卡片的多样化内容。',
          category: '分类C',
          date: '2024-01-20'
        },
        {
          title: '卡片标题 7',
          description: '这是第七个卡片的描述内容，测试多行布局的效果。',
          category: '分类A',
          date: '2024-01-21'
        },
        {
          title: '卡片标题 8',
          description: '这是第八个卡片的描述内容，完善整体的视觉效果。',
          category: '分类B',
          date: '2024-01-22'
        }
      ]
    }
  }
}
</script>

<style scoped>
.home {
  padding: 20px;
}

.home h2 {
  margin-bottom: 24px;
  color: #2c3e50;
  font-size: 24px;
  font-weight: 600;
}

.card-container {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 8px;
  width: 100%;
}

.card {
  background: #ffffff;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  min-height: 200px;
}

.card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.card-header {
  margin-bottom: 8px;
}

.card-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  line-height: 1.4;
}

.card-body {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.card-body p {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  flex: 1;
}

.card-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.tag {
  background: #42b983;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.date {
  font-size: 12px;
  color: #999;
}

.card-footer {
  margin-top: auto;
}

.btn-primary {
  width: 100%;
  padding: 8px 16px;
  background: #42b983;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.btn-primary:hover {
  background: #369970;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .card-container {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 768px) {
  .card-container {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .card-container {
    grid-template-columns: 1fr;
  }

  .home {
    padding: 16px;
  }
}
</style>